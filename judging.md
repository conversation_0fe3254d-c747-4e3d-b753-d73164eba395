это отчет к которому мой отчет об ошибке приставили как дупликат типа они похожи вот чужой отчет и комментарий судьи:
Issue #313
Submitted on 22 июля 2025 г. в 8:30:34 GMT+3

Improper Reward Distribution in Delegation & SymbioticNetworkMiddleware Contracts
Summary
The Delegation contract’s distributeRewards() function transfers the entire balance of a given reward token to an agent's network contract without accounting for agent-specific reward shares. Subsequently, the SymbioticNetworkMiddleware contract's distributeRewards() also blindly distributes the entire token balance it holds to the specified agent's staker rewarder. This leads to a complete misattribution of rewards, enabling malicious actors or accidental calls to drain or misallocate all tokens of that type intended for multiple agents, causing significant financial loss and griefing risks.

Root Cause
The core issue is lack of accounting granularity in reward distribution:

Delegation.distributeRewards() transfers all tokens of a reward asset it holds to the agent's network contract without tracking per-agent entitlements or the intended reward amount.

    /// @inheritdoc IDelegation  
    function distributeRewards(address _agent, address _asset) external {  
        DelegationStorage storage $ = getDelegationStorage();  
        uint256 _amount = IERC20(_asset).balanceOf(address(this));  
  
        uint256 totalCoverage = coverage(_agent);  
        if (totalCoverage == 0) return;  
  
        address network = $.agentData[_agent].network;  
        IERC20(_asset).safeTransfer(network, _amount);  
@>      ISymbioticNetworkMiddleware(network).distributeRewards(_agent, _asset);  
  
        emit DistributeReward(_agent, _asset, _amount);  
    }  
SymbioticNetworkMiddleware.distributeRewards() also reads and distributes its entire balance of the token to the agent's staker rewarder, again without specifying amounts or verifying entitlement.

    /// @inheritdoc ISymbioticNetworkMiddleware  
    function distributeRewards(address _agent, address _token) external checkAccess(this.distributeRewards.selector) {  
        SymbioticNetworkMiddlewareStorage storage $ = getSymbioticNetworkMiddlewareStorage();  
@>      uint256 _amount = IERC20(_token).balanceOf(address(this)); // @audit-issue: entire balance  
  
        address _vault = $.agentsToVault[_agent];  
        address stakerRewarder = $.vaults[_vault].stakerRewarder;  
  
        IERC20(_token).forceApprove(address(IStakerRewards(stakerRewarder)), _amount);  
        IStakerRewards(stakerRewarder).distributeRewards(  
            $.network, _token, _amount, abi.encode(uint48(block.timestamp - 1), $.feeAllowed, "", "")  
        );  
    }  
This double use of balanceOf(this) leads to entire token balances being attributed to a single agent regardless of the actual intended reward amounts. The contracts assume the entire balance belongs to the agent in question, enabling full token drainage or misallocation with a single call.

Internal Pre-conditions
Contract has received _token either via reward logic or external transfer

_agent has a valid associated vault with a stakerRewarder registered

External Pre-conditions
The contract must hold some _token balance

There are no token isolation mechanisms in place per agent or vault

Attack Path
Step 1: Unexpected or extra tokens accumulate in the Delegation contract’s balance for a particular reward token.

Step 2: Delegation.distributeRewards(_agent, _token) is invoked. It queries and transfers all of the token balance it holds to the agent’s associated network contract.

Step 3: The network contract (SymbioticNetworkMiddleware) receives the full token balance and calls its own distributeRewards(_agent, _token).

Step 4: The network contract’s method again reads its entire balance of the token and sends it all to the agent’s staker rewarder, regardless of how much is actually owed to that agent.

Step 5: Tokens intended for other agents (or surplus tokens) get fully drained by this single call, starving other agents of their rightful rewards.

1. Accidental Overdistribution:

A previous reward transfer sent extra _token tokens to the contract.

Caller triggers distributeRewards() for another _agent.

The extra balance gets erroneously distributed to the wrong party.

2. Cross-agent leakage:

If multiple agents are meant to receive different amounts of _token, one call to distributeRewards() can empty the contract and starve other agents.
Impact
High - Because the contracts send all of a reward token’s balance to just one agent without checking how much that agent actually earned, it means anyone calling distributeRewards() can accidentally or even maliciously drain all rewards meant for others. This can cause big financial losses for the protocol and leave honest agents without their fair share of rewards.

PoC
No response

Mitigation
Track per-agent rewards to prevent overdistribution.

Shiny Citron Crab
25 июля 2025 г. в 3:14:49 GMT+3

I think the report is invalid, misunderstanding of how protocol works.

The fund flows like the following

User -> Pays debt
Lender -> Realizes restaker interest -> Borrows interest from Vault -> Sends interest to Delegation
Delegation -> Transfer all received assets to Middleware and delegate reward distribution
Middleware -> Transfer all received assets to StakerRewards and call distributeRewards

All this fund flow is done in a single transaction. So Delegation contract is not meant to hold any asset before and after the transaction.


а вот мой отчет который типа дупликат:
Timing vulnerability causes unfair redistribution of restaker rewards between agent groups
Summary
A timing vulnerability causes unfair redistribution of restaker rewards between different agent groups. When restakers withdraw, coverage() immediately returns 0 while their funds remain locked in the vault during withdrawal period. If realizeRestakerInterest() is called for an agent with zero coverage, debt tokens are minted and funds transferred to Delegation, but distributeRewards() returns early leaving funds stuck. These orphaned funds are later incorrectly distributed to restakers of other agents since distributeRewards() uses balanceOf(address(this)) without separating funds by agent.

Root Cause
The root cause is that realizeRestakerInterest() performs irreversible operations (minting debt tokens and transferring funds to Delegation) before checking coverage in distributeRewards(). When coverage is zero, distributeRewards() returns early without distributing funds, leaving them stuck in the Delegation contract. These orphaned funds are later distributed to wrong restakers because distributeRewards() uses balanceOf(address(this)) to get ALL funds without separating them by agent.

Internal Pre-conditions
Agent needs to have accumulated interest to set accruedRestakerInterest to be greater than 0
System needs to have another agent to set coverage(otherAgent) to be greater than 0 for receiving misallocated funds
External Pre-conditions
Restakers need to initiate withdrawal to set coverage(_agent) to be exactly 0
Attack Path
1. Agent1 borrows funds when having non-zero coverage, accumulating restaker interest over time
2. Restakers withdraw delegation, setting agent's coverage to 0
The coverage() function returns 0 even when funds remain in Symbiotic because it uses current timestamp (block.timestamp) while the withdrawal process in Symbiotic creates a delay period. When restakers initiate withdrawal, their delegation becomes immediately inactive for coverage calculations, but the physical funds remain locked in the Symbiotic vault during the withdrawal period (e.g., 7 days). This creates a timing window where coverage() shows 0 but funds are still physically present and slashable in the vault.

3. Attacker calls realizeRestakerInterest(_agent, _asset) for agent with zero coverage
function coverage(address _agent) public view returns (uint256 delegation) {  
    DelegationStorage storage $ = getDelegationStorage();  
    uint256 _slashableCollateral = slashableCollateral(_agent);  
    uint256 currentdelegation = ISymbioticNetworkMiddleware($.agentData[_agent].network).coverage(_agent);  
    delegation = Math.min(_slashableCollateral, currentdelegation);  
}  
function coverage(address _agent) public view returns (uint256 delegation) {  
    uint48 _timestamp = uint48(block.timestamp);  //  current timestamp!  
    (delegation,) = coverageByVault(_network, _agent, _vault, _oracle, _timestamp);  
}  
4. Function mints debt tokens and transfers funds to Delegation contract but distributeRewards() returns early due to coverage check
function realizeRestakerInterest(ILender.LenderStorage storage $, address _agent, address _asset)  
        public  
        returns (uint256 realizedInterest)  
    {  
        ...  
        IDebtToken(reserve.debtToken).mint(_agent, realizedInterest + unrealizedInterest); // @audit empty debt tokens are minted for the agent  
        IVault(reserve.vault).borrow(_asset, realizedInterest, $.delegation);  
        IDelegation($.delegation).distributeRewards(_agent, _asset);  
        emit RealizeInterest(_asset, realizedInterest, $.delegation);  
    }  
https://github.com/sherlock-audit/2025-07-cap/blob/main/cap-contracts/contracts/delegation/Delegation.sol#L55-L67

5. Attacker calls realizeRestakerInterest(_otherAgent, _asset) for different agent with non-zero coverage
6. The orphaned funds from step 4 are incorrectly distributed to restakers of the second agent
Impact
The vulnerability creates two critical impacts:

Agents receive unbacked debt tokens without corresponding reward distribution to their restakers, creating artificial debt obligations.
Restakers of agents with zero coverage permanently lose their accumulated fees/rewards, while these orphaned funds are incorrectly distributed to restakers of different agents in subsequent calls. This breaks the core economic model by allowing systematic theft of restaker rewards and creating unbacked debt positions.
PoC
No response

Mitigation
Implement a deferred reward distribution system that preserves restaker earnings when coverage is temporarily zero. Instead of returning funds to vault (which causes permanent loss of restaker rewards), create a separate tracking mechanism that holds accumulated rewards per agent until coverage is restored.

Add fund segregation in distributeRewards() to prevent cross-agent contamination by tracking balances per agent separately rather than using the total contract balance. When an agent's coverage returns above zero, automatically distribute their accumulated rewards.

Additionally, consider adding a coverage check in realizeRestakerInterest() to prevent creating unbacked debt tokens when coverage is zero, or implement a rollback mechanism that can reverse the debt token minting if distribution fails.
