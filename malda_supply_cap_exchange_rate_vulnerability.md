# Malda Protocol - Supply Cap Exchange Rate Growth Vulnerability

## Description

Критическая уязвимость в механизме проверки `supplyCap` лимитов, которая может привести к полной блокировке mint операций из-за автоматического роста `exchangeRate` от накопления процентов, даже без новых депозитов пользователей.

## Vulnerability Details

### Проблемный код

В файле `malda-lending/src/Operator/Operator.sol`, функция `afterMTokenMint()`:

```solidity
function afterMTokenMint(address mToken) external view override {
    uint256 supplyCap = supplyCaps[mToken];
    // Supply cap of 0 corresponds to unlimited borrowing
    if (supplyCap != 0) {
        uint256 totalSupply = ImToken(mToken).totalSupply();
        Exp memory exchangeRate = Exp({mantissa: ImToken(mToken).exchangeRateStored()});
        uint256 totalAmount = mul_ScalarTruncate(exchangeRate, totalSupply);
        require(totalAmount <= supplyCap, Operator_MarketSupplyReached()); // ❌ Может заблокировать рынок
    }
}
```

### Корневая причина

1. **`supplyCap`** - фиксированный лимит в underlying токенах, устанавливаемый администраторами
2. **`exchangeRate`** - динамическое значение, которое автоматически растет от накопления процентов
3. **Проверка** происходит как: `totalSupply * exchangeRate <= supplyCap`
4. **Проблема**: `exchangeRate` растет со временем, даже если `totalSupply` не изменяется

### Механизм роста exchangeRate

Из `mTokenStorage.sol`, функция `_accrueInterest()`:

```solidity
function _accrueInterest() internal {
    uint256 interestAccumulated = borrowRate * blockDelta * totalBorrows;
    
    // totalBorrows увеличивается на ВСЮ сумму процентов
    uint256 totalBorrowsNew = interestAccumulated + borrowsPrior;
    
    // totalReserves увеличивается только на ЧАСТЬ процентов
    uint256 totalReservesNew = interestAccumulated * reserveFactor + reservesPrior;
    
    totalBorrows = totalBorrowsNew;    // ↑ +100% процентов
    totalReserves = totalReservesNew;  // ↑ +10% процентов (reserveFactor)
}
```

**Формула exchangeRate:**
```
exchangeRate = (totalCash + totalBorrows - totalReserves) / totalSupply
```

**Результат:** `(totalBorrows - totalReserves)` растет → `exchangeRate` растет

### Сценарий атаки/проблемы

**Начальное состояние (все работает):**
```
supplyCap = 1,000,000 USDC
totalSupply = 900,000 mUSDC
exchangeRate = 1.05
totalAmount = 900,000 * 1.05 = 945,000 USDC ✅ < 1,000,000
```

**Через время (проценты накапливаются):**
```
supplyCap = 1,000,000 USDC (тот же лимит)
totalSupply = 900,000 mUSDC (никто не минтил/не выводил)
exchangeRate = 1.15 (вырос из-за процентов!)
totalAmount = 900,000 * 1.15 = 1,035,000 USDC ❌ > 1,000,000
```

**Результат:** Любая попытка mint ревертится с `Operator_MarketSupplyReached()`

## Impact

### Критичность: HIGH

1. **Полная блокировка mint операций** - рынок становится недоступен для новых депозитов
2. **Автоматическое возникновение** - проблема появляется без внешнего воздействия
3. **Неожиданность для пользователей** - mint работал утром, не работает вечером
4. **Потеря доверия к протоколу** - пользователи не понимают причину блокировки

### Критический сценарий: Навсегда заблокированный рынок

При высокой утилизации (90%+) и jump rate model возможен сценарий где проценты накапливаются быстрее чем пользователи выводят средства:

**Условие блокировки:**
```
borrowRate * utilizationRate > redeemRate / (1 - utilizationRate)
```

**Пример:**
```
borrowRate = 50% APY (jump rate при 95% утилизации)
utilizationRate = 95%
redeemRate = 2% APY (пользователи не хотят выводить при высокой доходности)

50% * 95% = 47.5% > 2% / 5% = 40% ✓ → Навсегда заблокирован!
```

### Порочный круг

1. **Высокая утилизация** → высокие процентные ставки
2. **Высокие ставки** → быстрый рост `totalBorrows` и `exchangeRate`
3. **Быстрый рост** → превышение `supplyCap`
4. **Блокировка mint** → еще выше утилизация
5. **Цикл повторяется** до полного коллапса рынка

## Рекомендации по исправлению

### Вариант 1: Динамические supplyCap

```solidity
function afterMTokenMint(address mToken) external view override {
    uint256 supplyCap = supplyCaps[mToken];
    if (supplyCap != 0) {
        // Используем supplyCap в mTokens, не в underlying
        uint256 totalSupply = ImToken(mToken).totalSupply();
        require(totalSupply <= supplyCap, Operator_MarketSupplyReached());
    }
}
```

### Вариант 2: Автоматическая корректировка лимитов

```solidity
function getAdjustedSupplyCap(address mToken) internal view returns (uint256) {
    uint256 baseCap = supplyCaps[mToken];
    if (baseCap == 0) return 0;
    
    // Корректируем лимит на рост exchangeRate
    uint256 exchangeRate = ImToken(mToken).exchangeRateStored();
    uint256 initialRate = ImToken(mToken).initialExchangeRateMantissa();
    
    return baseCap * exchangeRate / initialRate;
}
```

### Вариант 3: Проверка до mint'а

```solidity
function beforeMTokenMint(address mToken, uint256 mintAmount) external override {
    uint256 supplyCap = supplyCaps[mToken];
    if (supplyCap != 0) {
        uint256 currentAmount = getCurrentUnderlyingSupply(mToken);
        require(currentAmount + mintAmount <= supplyCap, "Would exceed supply cap");
    }
}
```

### Вариант 4: Буферные лимиты

```solidity
mapping(address => uint256) public supplyCapBuffers; // Например, 20%

function afterMTokenMint(address mToken) external view override {
    uint256 supplyCap = supplyCaps[mToken];
    if (supplyCap != 0) {
        uint256 buffer = supplyCapBuffers[mToken];
        uint256 effectiveCap = supplyCap * (1e18 + buffer) / 1e18;
        
        uint256 totalAmount = getCurrentUnderlyingSupply(mToken);
        require(totalAmount <= effectiveCap, Operator_MarketSupplyReached());
    }
}
```

## Связанные проблемы

1. **Jump Rate Model** усугубляет проблему экспоненциальным ростом ставок
2. **Высокая утилизация** создает порочный круг блокировки
3. **Отсутствие мониторинга** роста exchangeRate в административных процессах

## Заключение

Уязвимость Supply Cap Exchange Rate Growth представляет критическую угрозу для функционирования протокола Malda. Автоматический рост `exchangeRate` от накопления процентов может полностью заблокировать рынки для новых депозитов, создавая неожиданные проблемы для пользователей и потенциально приводя к навсегда заблокированным рынкам при высокой утилизации.

Необходимо немедленное исправление через изменение логики проверки `supplyCap` лимитов или внедрение динамических механизмов корректировки лимитов.
